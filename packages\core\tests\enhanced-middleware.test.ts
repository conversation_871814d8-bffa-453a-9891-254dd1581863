import { Context } from "koishi";
import { MiddlewareEventBus } from "../src/shared/events/MiddlewareEventBus";
import { MultiResponseManager } from "../src/middlewares/core/MultiResponseManager";
import { EnhancedMiddlewareContext } from "../src/middlewares/core/EnhancedMiddlewareContext";
import { 
    ResponseType, 
    MiddlewarePhase, 
    ExecutionState 
} from "../src/middlewares/core/MiddlewareCore";
import { MessageContext } from "../src/middlewares/base";

/**
 * 增强中间件功能测试
 */
describe("Enhanced Middleware Features", () => {
    let ctx: Context;
    let eventBus: MiddlewareEventBus;
    let multiResponseManager: MultiResponseManager;
    let mockMessageContext: MessageContext;

    beforeEach(async () => {
        // 创建模拟的 Koishi Context
        ctx = {
            logger: {
                info: jest.fn(),
                debug: jest.fn(),
                warn: jest.fn(),
                error: jest.fn(),
            }
        } as any;

        // 初始化服务
        eventBus = new MiddlewareEventBus(ctx);
        multiResponseManager = new MultiResponseManager(ctx, eventBus);

        // 创建模拟的消息上下文
        mockMessageContext = {
            state: "idle",
            koishiContext: ctx,
            koishiSession: {
                platform: "test",
                channelId: "test-channel",
                userId: "test-user",
                messageId: "test-message",
                content: "测试消息",
                timestamp: Date.now(),
                stripped: { atSelf: true }
            },
            allowedChannels: ["test-channel"],
            currentTurnId: "test-turn-123",
            agentResponses: [],
            heartbeatCount: 0,
            isMentioned: true,
            platform: {
                sendMessage: jest.fn().mockResolvedValue(undefined)
            },
            transitionTo: jest.fn().mockResolvedValue(undefined)
        } as any;
    });

    afterEach(async () => {
        await multiResponseManager.stop();
        await eventBus.stop();
    });

    describe("MiddlewareEventBus", () => {
        test("should emit and handle events", async () => {
            const handler = jest.fn();
            
            eventBus.on("test_event", { handle: handler });
            
            await eventBus.emit({
                id: 1,
                type: "test_event",
                timestamp: new Date(),
                data: "test data"
            } as any);

            expect(handler).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "test_event",
                    data: "test data"
                })
            );
        });

        test("should wait for events with timeout", async () => {
            const eventPromise = eventBus.waitForEvent("test_wait_event", undefined, 1000);
            
            // 延迟发射事件
            setTimeout(() => {
                eventBus.emit({
                    id: 2,
                    type: "test_wait_event",
                    timestamp: new Date(),
                    result: "success"
                } as any);
            }, 100);

            const event = await eventPromise;
            expect(event.result).toBe("success");
        });

        test("should timeout when waiting for events", async () => {
            await expect(
                eventBus.waitForEvent("non_existent_event", undefined, 100)
            ).rejects.toThrow("等待事件 non_existent_event 超时");
        });
    });

    describe("MultiResponseManager", () => {
        test("should queue responses with priority", async () => {
            const turnId = "test-turn-456";

            // 添加不同优先级的响应
            await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.THINKING, 
                "思考中...", 
                { priority: 3 }
            );

            await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.FINAL, 
                "最终答案", 
                { priority: 10 }
            );

            await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.INTERMEDIATE, 
                "中间结果", 
                { priority: 5 }
            );

            const queue = multiResponseManager.getResponseQueue(turnId);
            
            // 验证队列按优先级排序
            expect(queue).toHaveLength(3);
            expect(queue[0].priority).toBe(10); // 最高优先级在前
            expect(queue[1].priority).toBe(5);
            expect(queue[2].priority).toBe(3);
        });

        test("should provide response statistics", async () => {
            const turnId = "test-turn-789";

            await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.THINKING, 
                "思考1"
            );
            await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.THINKING, 
                "思考2"
            );

            const stats = multiResponseManager.getResponseStats(turnId);
            
            expect(stats.total).toBe(2);
            expect(stats.pending).toBe(2);
            expect(stats.sent).toBe(0);
        });

        test("should cancel responses", async () => {
            const turnId = "test-turn-cancel";
            
            const responseId = await multiResponseManager.queueResponse(
                turnId, 
                ResponseType.THINKING, 
                "待取消的响应"
            );

            const cancelled = await multiResponseManager.cancelResponse(turnId, responseId);
            expect(cancelled).toBe(true);

            const queue = multiResponseManager.getResponseQueue(turnId);
            const response = queue.find(r => r.id === responseId);
            expect(response?.sendStatus).toBe("cancelled");
        });
    });

    describe("EnhancedMiddlewareContext", () => {
        let enhancedCtx: EnhancedMiddlewareContext;

        beforeEach(() => {
            enhancedCtx = new EnhancedMiddlewareContext(mockMessageContext, eventBus);
        });

        afterEach(() => {
            enhancedCtx.dispose();
        });

        test("should manage execution state", () => {
            expect(enhancedCtx.metadata.executionState).toBe(ExecutionState.IDLE);

            enhancedCtx.pause("测试暂停");
            expect(enhancedCtx.metadata.executionState).toBe(ExecutionState.PAUSED);
            expect(enhancedCtx.metadata.pauseReason).toBe("测试暂停");

            enhancedCtx.resume();
            expect(enhancedCtx.metadata.executionState).toBe(ExecutionState.RUNNING);
            expect(enhancedCtx.metadata.pauseReason).toBeUndefined();
        });

        test("should manage breakpoints", () => {
            const breakpoint = {
                phase: MiddlewarePhase.CORE_PROCESSING,
                action: "pause" as const
            };

            enhancedCtx.addBreakpoint(breakpoint);
            expect(enhancedCtx.metadata.breakpoints).toContain(breakpoint);

            enhancedCtx.removeBreakpoint(MiddlewarePhase.CORE_PROCESSING);
            expect(enhancedCtx.metadata.breakpoints).toHaveLength(0);
        });

        test("should check pause conditions", () => {
            // 添加断点
            enhancedCtx.addBreakpoint({
                phase: MiddlewarePhase.CORE_PROCESSING,
                action: "pause"
            });

            // 设置当前阶段
            enhancedCtx.metadata.currentPhase = MiddlewarePhase.CORE_PROCESSING;

            expect(enhancedCtx.shouldPause()).toBe(true);
        });

        test("should manage responses", async () => {
            const responseId = enhancedCtx.addResponse(
                ResponseType.THINKING, 
                "测试思考", 
                { source: "test" }
            );

            expect(responseId).toBeDefined();
            expect(enhancedCtx.responses).toHaveLength(1);

            const responses = enhancedCtx.getResponses(ResponseType.THINKING);
            expect(responses).toHaveLength(1);
            expect(responses[0].content).toBe("测试思考");
            expect(responses[0].metadata?.source).toBe("test");
        });

        test("should send different types of responses", async () => {
            await enhancedCtx.sendThinking("思考过程");
            await enhancedCtx.sendIntermediate("中间结果");
            await enhancedCtx.sendFinal("最终答案");

            expect(enhancedCtx.responses).toHaveLength(3);
            expect(enhancedCtx.getResponses(ResponseType.THINKING)).toHaveLength(1);
            expect(enhancedCtx.getResponses(ResponseType.INTERMEDIATE)).toHaveLength(1);
            expect(enhancedCtx.getResponses(ResponseType.FINAL)).toHaveLength(1);
        });

        test("should handle event communication", async () => {
            const handler = jest.fn();
            
            enhancedCtx.onEvent("test_context_event", handler);
            
            await enhancedCtx.emitEvent({
                id: 3,
                type: "test_context_event",
                timestamp: new Date(),
                message: "测试消息"
            });

            expect(handler).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "test_context_event",
                    message: "测试消息"
                })
            );
        });

        test("should update shared context with notifications", async () => {
            const eventHandler = jest.fn();
            eventBus.onMiddlewareEvent("context_updated", { handle: eventHandler });

            await enhancedCtx.updateSharedContext("testKey", "testValue", "test-source");

            expect(enhancedCtx.shared.get("testKey")).toBe("testValue");
            expect(eventHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "context_updated",
                    key: "testKey",
                    value: "testValue",
                    source: "test-source"
                })
            );
        });
    });

    describe("Integration Tests", () => {
        test("should coordinate between components", async () => {
            const enhancedCtx = new EnhancedMiddlewareContext(mockMessageContext, eventBus);
            const turnId = enhancedCtx.currentTurnId;

            // 监听响应生成事件
            const responseHandler = jest.fn();
            eventBus.onMiddlewareEvent("response_generated", { handle: responseHandler });

            // 通过上下文添加响应
            enhancedCtx.addResponse(ResponseType.THINKING, "集成测试思考");

            // 验证事件被触发
            expect(responseHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "response_generated",
                    responseType: ResponseType.THINKING,
                    content: "集成测试思考"
                })
            );

            enhancedCtx.dispose();
        });
    });
});

// Jest 配置（如果需要）
export const jestConfig = {
    testEnvironment: "node",
    setupFilesAfterEnv: ["<rootDir>/tests/setup.ts"],
    testMatch: ["**/*.test.ts"],
    collectCoverageFrom: [
        "src/**/*.ts",
        "!src/**/*.d.ts",
        "!src/**/index.ts"
    ]
};
