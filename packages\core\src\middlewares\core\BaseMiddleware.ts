import { Context } from "koishi";
import {
    IMiddleware,
    MiddlewareContext,
    MiddlewarePhase,
    MiddlewarePriority,
    MiddlewareInfo,
    MiddlewareDefinition,
} from "./MiddlewareCore";

/**
 * 抽象中间件基类
 * 提供通用的中间件实现基础
 */
export abstract class BaseMiddleware<TConfig = any> implements IMiddleware<TConfig> {
    public readonly id: string;
    public readonly name: string;
    public readonly phase: MiddlewarePhase;
    public readonly priority: MiddlewarePriority;
    public readonly enabled: boolean;
    public readonly dependencies: string[];
    public readonly config: TConfig;

    protected readonly ctx: Context;
    protected readonly logger: any;

    constructor(
        ctx: Context,
        config: TConfig,
        options: {
            enabled?: boolean;
            // 允许覆盖装饰器中的元数据（用于特殊情况）
            override?: Partial<MiddlewareDecoratorMetadata>;
        } = {}
    ) {
        // 从装饰器元数据中提取信息
        const metadata = getMiddlewareMetadata(this.constructor as any);
        if (!metadata) {
            throw new Error(`中间件类 ${this.constructor.name} 缺少 @middleware 装饰器`);
        }

        // 应用覆盖选项
        const finalMetadata = { ...metadata, ...options.override };

        this.id = finalMetadata.id;
        this.name = finalMetadata.name;
        this.phase = finalMetadata.phase;
        this.priority = finalMetadata.priority ?? MiddlewarePriority.NORMAL;
        this.dependencies = finalMetadata.dependencies ?? [];
        this.enabled = options.enabled ?? true;
        this.ctx = ctx;
        this.config = config;
        this.logger = ctx.logger(`Middleware:${this.name}`);
    }

    /**
     * 执行中间件逻辑 - 子类必须实现
     */
    abstract execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void>;

    /**
     * 初始化中间件 - 子类可选实现
     */
    async initialize?(): Promise<void>;

    /**
     * 清理资源 - 子类可选实现
     */
    async dispose?(): Promise<void>;

    /**
     * 健康检查 - 子类可选实现
     */
    async healthCheck?(): Promise<boolean>;

    /**
     * 获取中间件信息
     */
    getInfo(): MiddlewareInfo {
        return {
            id: this.id,
            name: this.name,
            phase: this.phase,
            priority: this.priority,
            enabled: this.enabled,
            dependencies: this.dependencies,
        };
    }

    /**
     * 记录性能指标
     */
    protected recordMetric(ctx: MiddlewareContext, key: string, value: number): void {
        ctx.metadata.performance.set(`${this.id}.${key}`, value);
    }

    /**
     * 获取共享数据
     */
    protected getShared<T>(ctx: MiddlewareContext, key: string): T | undefined {
        return ctx.shared.get(key);
    }

    /**
     * 设置共享数据
     */
    protected setShared<T>(ctx: MiddlewareContext, key: string, value: T): void {
        ctx.shared.set(key, value);
    }

    /**
     * 检查依赖是否已执行
     */
    protected checkDependencies(ctx: MiddlewareContext): boolean {
        return this.dependencies.every((dep) => ctx.metadata.executedMiddlewares.includes(dep));
    }

    /**
     * 安全执行异步操作
     */
    protected async safeExecute<T>(operation: () => Promise<T>, errorMessage: string, defaultValue?: T): Promise<T | undefined> {
        try {
            return await operation();
        } catch (error) {
            this.logger.error(`${errorMessage}:`, error);
            return defaultValue;
        }
    }
}

/**
 * 预处理阶段中间件基类
 */
export abstract class PreprocessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.PREPROCESSING,
                ...options?.override,
            },
        });
    }
}

/**
 * 输入处理阶段中间件基类
 */
export abstract class InputProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.INPUT_PROCESSING,
                ...options?.override,
            },
        });
    }
}

/**
 * 条件检查阶段中间件基类
 */
export abstract class ConditionCheckMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.CONDITION_CHECK,
                ...options?.override,
            },
        });
    }
}

/**
 * 核心处理阶段中间件基类
 */
export abstract class CoreProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.CORE_PROCESSING,
                ...options?.override,
            },
        });
    }
}

/**
 * 输出处理阶段中间件基类
 */
export abstract class OutputProcessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.OUTPUT_PROCESSING,
                ...options?.override,
            },
        });
    }
}

/**
 * 后处理阶段中间件基类
 */
export abstract class PostprocessingMiddleware<TConfig = any> extends BaseMiddleware<TConfig> {
    constructor(
        ctx: Context,
        config: TConfig,
        options?: {
            enabled?: boolean;
            override?: Partial<MiddlewareDecoratorMetadata>;
        }
    ) {
        super(ctx, config, {
            ...options,
            override: {
                phase: MiddlewarePhase.POSTPROCESSING,
                ...options?.override,
            },
        });
    }
}

/**
 * 中间件元数据接口
 */
export interface MiddlewareDecoratorMetadata {
    id: string;
    name: string;
    phase: MiddlewarePhase;
    priority?: MiddlewarePriority;
    dependencies?: string[];
    version?: string;
    description?: string;
    author?: string;
}

/**
 * 中间件装饰器 - 用于自动注册中间件
 */
export function middleware(metadata: MiddlewareDecoratorMetadata) {
    return function <T extends new (...args: any[]) => BaseMiddleware>(constructor: T) {
        // 在构造函数上添加元数据
        (constructor as any).middlewareMetadata = {
            ...metadata,
            priority: metadata.priority ?? MiddlewarePriority.NORMAL,
            dependencies: metadata.dependencies ?? [],
        };
        return constructor;
    };
}

/**
 * 配置验证装饰器
 */
export function validateConfig<TConfig>(validator: (config: TConfig) => boolean | string) {
    return function <T extends new (...args: any[]) => BaseMiddleware<TConfig>>(constructor: T) {
        (constructor as any).configValidator = validator;
        return constructor;
    };
}

/**
 * 从中间件类中提取装饰器元数据
 */
export function getMiddlewareMetadata<T extends new (...args: any[]) => BaseMiddleware>(
    constructor: T
): MiddlewareDecoratorMetadata | null {
    return (constructor as any).middlewareMetadata || null;
}

/**
 * 从中间件类中提取配置验证器
 */
export function getConfigValidator<TConfig>(
    constructor: new (...args: any[]) => BaseMiddleware<TConfig>
): ((config: TConfig) => boolean | string) | null {
    return (constructor as any).configValidator || null;
}

/**
 * 检查类是否有中间件装饰器
 */
export function hasMiddlewareMetadata<T extends new (...args: any[]) => BaseMiddleware>(constructor: T): boolean {
    return !!(constructor as any).middlewareMetadata;
}

/**
 * 从中间件类自动创建中间件定义
 */
export function createMiddlewareDefinition<TConfig = any>(
    middlewareClass: new (ctx: Context, config: TConfig) => BaseMiddleware<TConfig>,
    options: {
        defaultConfig?: TConfig;
        validateConfig?: (config: TConfig) => boolean | string;
    } = {}
): MiddlewareDefinition<TConfig> {
    // 获取装饰器元数据
    const metadata = getMiddlewareMetadata(middlewareClass);
    if (!metadata) {
        throw new Error(`中间件类 ${middlewareClass.name} 缺少 @middleware 装饰器`);
    }

    // 获取配置验证器（优先使用传入的，然后使用装饰器的）
    const configValidator = options.validateConfig || getConfigValidator(middlewareClass);

    return {
        factory: (ctx: Context, config: TConfig) => new middlewareClass(ctx, config),
        defaultConfig: options.defaultConfig,
        validateConfig: configValidator || undefined,
        metadata: {
            id: metadata.id,
            name: metadata.name,
            phase: metadata.phase,
            priority: metadata.priority ?? MiddlewarePriority.NORMAL,
            dependencies: metadata.dependencies ?? [],
            version: metadata.version,
            description: metadata.description,
            author: metadata.author,
        },
    };
}

/**
 * 批量创建中间件定义
 */
export function createMiddlewareDefinitions<TConfigs extends Record<string, any>>(middlewareClasses: {
    [K in keyof TConfigs]: {
        class: new (ctx: Context, config: TConfigs[K]) => BaseMiddleware<TConfigs[K]>;
        defaultConfig?: TConfigs[K];
        validateConfig?: (config: TConfigs[K]) => boolean | string;
    };
}): Map<string, MiddlewareDefinition> {
    const definitions = new Map<string, MiddlewareDefinition>();

    for (const [key, { class: middlewareClass, defaultConfig, validateConfig }] of Object.entries(middlewareClasses)) {
        const definition = createMiddlewareDefinition(middlewareClass as any, {
            defaultConfig,
            validateConfig,
        });
        definitions.set(definition.metadata.id, definition);
    }

    return definitions;
}
