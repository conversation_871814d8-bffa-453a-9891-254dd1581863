import { Context, Session } from "koishi";
import { EnhancedMiddlewarePipeline } from "../src/middlewares/core/EnhancedMiddlewarePipeline";
import { EnhancedMiddlewareContext } from "../src/middlewares/core/EnhancedMiddlewareContext";
import { MiddlewareEventBus } from "../src/shared/events/MiddlewareEventBus";
import { MultiResponseManager } from "../src/middlewares/core/MultiResponseManager";
import {
    MiddlewarePhase,
    ResponseType,
    ExecutionState
} from "../src/middlewares/core/MiddlewareCore";
import { MiddlewareRegistry } from "../src/middlewares/registry/MiddlewareRegistry";
import { MessageContext } from "../src/middlewares/base";

/**
 * 增强中间件功能使用示例
 */
export class EnhancedMiddlewareExample {
    private pipeline: EnhancedMiddlewarePipeline;
    private eventBus: MiddlewareEventBus;
    private multiResponseManager: MultiResponseManager;
    private registry: MiddlewareRegistry;

    constructor(private ctx: Context) {
        this.eventBus = new MiddlewareEventBus(ctx);
        this.multiResponseManager = new MultiResponseManager(ctx, this.eventBus);
        this.registry = new MiddlewareRegistry(ctx);
        this.pipeline = new EnhancedMiddlewarePipeline(ctx, this.registry, this.eventBus);

        this.setupEventListeners();
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 监听中间件执行事件
        this.eventBus.onMiddlewareEvent("middleware_started", {
            handle: (event) => {
                console.log(`🚀 中间件开始执行: ${event.middlewareName} (${event.phase})`);
            }
        });

        this.eventBus.onMiddlewareEvent("middleware_completed", {
            handle: (event) => {
                console.log(`✅ 中间件执行完成: ${event.middlewareName} (耗时: ${event.duration}ms)`);
            }
        });

        this.eventBus.onMiddlewareEvent("middleware_failed", {
            handle: (event) => {
                console.log(`❌ 中间件执行失败: ${event.middlewareName} - ${event.error}`);
            }
        });

        // 监听执行控制事件
        this.eventBus.onMiddlewareEvent("execution_paused", {
            handle: (event) => {
                console.log(`⏸️ 执行暂停: ${event.reason} (阶段: ${event.currentPhase})`);
            }
        });

        this.eventBus.onMiddlewareEvent("execution_resumed", {
            handle: (event) => {
                console.log(`▶️ 执行恢复: 从 ${event.fromPhase} 阶段继续`);
            }
        });

        // 监听响应事件
        this.eventBus.onMiddlewareEvent("response_generated", {
            handle: (event) => {
                console.log(`💬 生成响应: ${event.responseType} - ${event.content.substring(0, 50)}...`);
            }
        });
    }

    /**
     * 示例1: 基本的多响应处理
     */
    async demonstrateMultiResponse(): Promise<void> {
        console.log("\n=== 示例1: 多响应处理 ===");

        // 创建模拟的消息上下文
        const messageContext = await this.createMockMessageContext();
        const enhancedCtx = new EnhancedMiddlewareContext(messageContext, this.eventBus);

        // 添加思考过程响应
        await enhancedCtx.sendThinking("让我思考一下这个问题...");

        // 添加中间结果响应
        await enhancedCtx.sendIntermediate("我找到了一些相关信息");
        await enhancedCtx.sendIntermediate("正在分析数据...");

        // 添加最终响应
        await enhancedCtx.sendFinal("基于分析，我的建议是...");

        // 查看响应统计
        const responses = enhancedCtx.getResponses();
        console.log(`总共生成了 ${responses.length} 个响应:`);
        responses.forEach((r, i) => {
            console.log(`  ${i + 1}. [${r.type}] ${r.content.substring(0, 30)}...`);
        });
    }

    /**
     * 示例2: 执行控制 - 暂停和恢复
     */
    async demonstrateExecutionControl(): Promise<void> {
        console.log("\n=== 示例2: 执行控制 ===");

        const messageContext = await this.createMockMessageContext();
        const enhancedCtx = new EnhancedMiddlewareContext(messageContext, this.eventBus);

        // 添加断点
        enhancedCtx.addBreakpoint({
            phase: MiddlewarePhase.CORE_PROCESSING,
            action: "pause"
        });

        console.log("添加了断点，执行将在核心处理阶段暂停");

        // 模拟执行到断点
        enhancedCtx.metadata.currentPhase = MiddlewarePhase.CORE_PROCESSING;

        if (enhancedCtx.shouldPause()) {
            enhancedCtx.pause("到达断点");
            console.log("执行已暂停");

            // 模拟一些处理时间
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 恢复执行
            enhancedCtx.resume();
            console.log("执行已恢复");
        }
    }

    /**
     * 示例3: 事件驱动通信
     */
    async demonstrateEventCommunication(): Promise<void> {
        console.log("\n=== 示例3: 事件驱动通信 ===");

        const messageContext = await this.createMockMessageContext();
        const enhancedCtx = new EnhancedMiddlewareContext(messageContext, this.eventBus);

        // 订阅自定义事件
        enhancedCtx.onEvent("custom_data_processed", (event: any) => {
            console.log(`收到自定义事件: ${event.data}`);
        });

        // 发射自定义事件
        await enhancedCtx.emitEvent({
            id: Date.now(),
            type: "custom_data_processed",
            timestamp: new Date(),
            data: "处理完成的数据"
        });

        // 等待事件（带超时）
        try {
            const event = await enhancedCtx.waitForEvent(
                "state_changed",
                (e: any) => e.newState === "completed",
                5000
            );
            console.log("等待到了状态变更事件:", event);
        } catch (error) {
            console.log("等待事件超时");
        }
    }

    /**
     * 示例4: 完整的管道执行流程
     */
    async demonstrateFullPipeline(): Promise<void> {
        console.log("\n=== 示例4: 完整管道执行 ===");

        // 构建管道（这里需要实际的中间件实现）
        // this.pipeline.add("builtin.error-handling");
        // this.pipeline.add("builtin.database-storage");
        // this.pipeline.add("builtin.reply-condition");
        // this.pipeline.add("builtin.llm-processing");
        // this.pipeline.add("builtin.enhanced-response-handling");

        // await this.pipeline.build();

        const messageContext = await this.createMockMessageContext();

        console.log("开始执行中间件管道...");

        try {
            // await this.pipeline.execute(messageContext);
            console.log("管道执行完成");
        } catch (error) {
            console.error("管道执行失败:", error);
        }
    }

    /**
     * 示例5: 响应队列管理
     */
    async demonstrateResponseQueue(): Promise<void> {
        console.log("\n=== 示例5: 响应队列管理 ===");

        const turnId = "test-turn-123";

        // 添加不同优先级的响应到队列
        await this.multiResponseManager.queueResponse(
            turnId,
            ResponseType.THINKING,
            "思考中...",
            { priority: 3, delay: 1000 }
        );

        await this.multiResponseManager.queueResponse(
            turnId,
            ResponseType.INTERMEDIATE,
            "中间结果1",
            { priority: 5 }
        );

        await this.multiResponseManager.queueResponse(
            turnId,
            ResponseType.FINAL,
            "最终答案",
            { priority: 10 }
        );

        // 查看队列状态
        const queue = this.multiResponseManager.getResponseQueue(turnId);
        console.log(`队列中有 ${queue.length} 个响应:`);
        queue.forEach((r, i) => {
            console.log(`  ${i + 1}. [${r.type}] 优先级:${r.priority} 状态:${r.sendStatus}`);
        });

        // 查看统计信息
        const stats = this.multiResponseManager.getResponseStats(turnId);
        console.log("响应统计:", stats);

        // 清理队列
        setTimeout(() => {
            this.multiResponseManager.clearResponseQueue(turnId);
            console.log("队列已清理");
        }, 5000);
    }

    /**
     * 创建模拟的消息上下文
     */
    private async createMockMessageContext(): Promise<MessageContext> {
        // 创建模拟的 Koishi Session
        const mockSession = {
            platform: "test",
            channelId: "test-channel",
            userId: "test-user",
            messageId: "test-message",
            content: "测试消息",
            timestamp: Date.now(),
            stripped: { atSelf: true }
        } as Session;

        // 创建消息上下文
        return await MessageContext.create(this.ctx, mockSession, ["test-channel"]);
    }

    /**
     * 运行所有示例
     */
    async runAllExamples(): Promise<void> {
        console.log("🎯 开始运行增强中间件功能示例\n");

        try {
            await this.demonstrateMultiResponse();
            await this.demonstrateExecutionControl();
            await this.demonstrateEventCommunication();
            await this.demonstrateResponseQueue();
            // await this.demonstrateFullPipeline();

            console.log("\n✨ 所有示例运行完成!");
        } catch (error) {
            console.error("示例运行失败:", error);
        }
    }

    /**
     * 清理资源
     */
    async cleanup(): Promise<void> {
        await this.pipeline.dispose();
        await this.multiResponseManager.stop();
        await this.eventBus.stop();
    }
}

// 使用示例
export async function runEnhancedMiddlewareExamples(ctx: Context): Promise<void> {
    const example = new EnhancedMiddlewareExample(ctx);

    try {
        await example.runAllExamples();
    } finally {
        await example.cleanup();
    }
}
