import { Context, Service, randomId } from "koishi";
import { ResponseType, ResponseItem } from "./MiddlewareCore";
import { MiddlewareEventBus } from "../../shared/events/MiddlewareEventBus";
import { 
    ResponseGeneratedEvent, 
    ResponseQueuedEvent, 
    ResponseSentEvent 
} from "../../shared/events/types";

declare module "koishi" {
    interface Context {
        "yesimbot.multiResponse": MultiResponseManager;
    }
}

/**
 * 响应发送状态
 */
export enum ResponseSendStatus {
    PENDING = "pending",
    SENDING = "sending",
    SENT = "sent",
    FAILED = "failed",
    CANCELLED = "cancelled",
}

/**
 * 排队的响应项
 */
export interface QueuedResponse extends ResponseItem {
    turnId: string;
    sendStatus: ResponseSendStatus;
    priority: number;
    delay?: number; // 发送延迟（毫秒）
    retryCount: number;
    maxRetries: number;
    error?: string;
    sentAt?: Date;
}

/**
 * 响应发送配置
 */
export interface ResponseSendConfig {
    priority?: number;
    delay?: number;
    maxRetries?: number;
    immediate?: boolean; // 是否立即发送，不进入队列
}

/**
 * 多响应管理器
 * 管理一次对话中的多个响应，支持队列、优先级、重试等功能
 */
export class MultiResponseManager extends Service {
    private responseQueues = new Map<string, QueuedResponse[]>(); // turnId -> responses
    private processingTurns = new Set<string>();
    private sendingResponses = new Map<string, QueuedResponse>(); // responseId -> response
    
    private config = {
        maxQueueSize: 50,
        defaultPriority: 5,
        defaultMaxRetries: 3,
        processingInterval: 100, // 处理队列的间隔（毫秒）
        maxConcurrentSends: 3,
    };

    private processingTimer?: NodeJS.Timeout;

    constructor(ctx: Context, private eventBus: MiddlewareEventBus) {
        super(ctx, "yesimbot.multiResponse", true);
        this.startProcessing();
    }

    /**
     * 添加响应到队列
     */
    async queueResponse(
        turnId: string,
        type: ResponseType,
        content: string,
        config: ResponseSendConfig = {},
        metadata?: Record<string, any>
    ): Promise<string> {
        const response: QueuedResponse = {
            id: randomId(),
            type,
            content,
            timestamp: new Date(),
            metadata,
            turnId,
            sendStatus: ResponseSendStatus.PENDING,
            priority: config.priority ?? this.config.defaultPriority,
            delay: config.delay,
            retryCount: 0,
            maxRetries: config.maxRetries ?? this.config.defaultMaxRetries,
        };

        // 如果设置为立即发送，直接发送
        if (config.immediate) {
            await this.sendResponseImmediately(response);
            return response.id;
        }

        // 添加到队列
        if (!this.responseQueues.has(turnId)) {
            this.responseQueues.set(turnId, []);
        }

        const queue = this.responseQueues.get(turnId)!;
        
        // 检查队列大小限制
        if (queue.length >= this.config.maxQueueSize) {
            throw new Error(`响应队列已满 (turnId: ${turnId})`);
        }

        queue.push(response);
        
        // 按优先级排序（高优先级在前）
        queue.sort((a, b) => b.priority - a.priority);

        // 发射队列事件
        await this.eventBus.emitMiddlewareEvent<ResponseQueuedEvent>({
            id: Date.now(),
            type: "response_queued",
            timestamp: new Date(),
            turnId,
            responseId: response.id,
            queuePosition: queue.indexOf(response)
        });

        this.ctx.logger.debug(`响应已加入队列 (turnId: ${turnId}, responseId: ${response.id}, priority: ${response.priority})`);
        
        return response.id;
    }

    /**
     * 立即发送响应（不进入队列）
     */
    async sendResponseImmediately(response: QueuedResponse): Promise<void> {
        response.sendStatus = ResponseSendStatus.SENDING;
        this.sendingResponses.set(response.id, response);

        try {
            await this.performSend(response);
            response.sendStatus = ResponseSendStatus.SENT;
            response.sentAt = new Date();

            // 发射发送成功事件
            await this.eventBus.emitMiddlewareEvent<ResponseSentEvent>({
                id: Date.now(),
                type: "response_sent",
                timestamp: new Date(),
                turnId: response.turnId,
                responseId: response.id,
                success: true
            });

        } catch (error) {
            response.sendStatus = ResponseSendStatus.FAILED;
            response.error = (error as Error).message;

            // 发射发送失败事件
            await this.eventBus.emitMiddlewareEvent<ResponseSentEvent>({
                id: Date.now(),
                type: "response_sent",
                timestamp: new Date(),
                turnId: response.turnId,
                responseId: response.id,
                success: false
            });

            throw error;
        } finally {
            this.sendingResponses.delete(response.id);
        }
    }

    /**
     * 获取Turn的响应队列
     */
    getResponseQueue(turnId: string): QueuedResponse[] {
        return [...(this.responseQueues.get(turnId) || [])];
    }

    /**
     * 获取Turn的响应统计
     */
    getResponseStats(turnId: string): {
        total: number;
        pending: number;
        sending: number;
        sent: number;
        failed: number;
        cancelled: number;
    } {
        const queue = this.responseQueues.get(turnId) || [];
        
        return {
            total: queue.length,
            pending: queue.filter(r => r.sendStatus === ResponseSendStatus.PENDING).length,
            sending: queue.filter(r => r.sendStatus === ResponseSendStatus.SENDING).length,
            sent: queue.filter(r => r.sendStatus === ResponseSendStatus.SENT).length,
            failed: queue.filter(r => r.sendStatus === ResponseSendStatus.FAILED).length,
            cancelled: queue.filter(r => r.sendStatus === ResponseSendStatus.CANCELLED).length,
        };
    }

    /**
     * 取消响应
     */
    async cancelResponse(turnId: string, responseId: string): Promise<boolean> {
        const queue = this.responseQueues.get(turnId);
        if (!queue) return false;

        const response = queue.find(r => r.id === responseId);
        if (!response) return false;

        if (response.sendStatus === ResponseSendStatus.PENDING) {
            response.sendStatus = ResponseSendStatus.CANCELLED;
            return true;
        }

        return false;
    }

    /**
     * 清空Turn的响应队列
     */
    clearResponseQueue(turnId: string): void {
        const queue = this.responseQueues.get(turnId);
        if (queue) {
            // 取消所有待发送的响应
            queue.forEach(response => {
                if (response.sendStatus === ResponseSendStatus.PENDING) {
                    response.sendStatus = ResponseSendStatus.CANCELLED;
                }
            });
        }
        
        this.responseQueues.delete(turnId);
        this.processingTurns.delete(turnId);
    }

    /**
     * 开始处理队列
     */
    private startProcessing(): void {
        this.processingTimer = setInterval(() => {
            this.processQueues().catch(error => {
                this.ctx.logger.error("处理响应队列时发生错误:", error);
            });
        }, this.config.processingInterval);
    }

    /**
     * 处理所有队列
     */
    private async processQueues(): Promise<void> {
        const currentSendingCount = this.sendingResponses.size;
        if (currentSendingCount >= this.config.maxConcurrentSends) {
            return; // 达到并发限制
        }

        for (const [turnId, queue] of this.responseQueues) {
            if (this.processingTurns.has(turnId)) {
                continue; // 该Turn正在处理中
            }

            const pendingResponse = queue.find(r => r.sendStatus === ResponseSendStatus.PENDING);
            if (!pendingResponse) {
                continue; // 没有待发送的响应
            }

            // 检查延迟
            if (pendingResponse.delay) {
                const elapsed = Date.now() - pendingResponse.timestamp.getTime();
                if (elapsed < pendingResponse.delay) {
                    continue; // 还未到发送时间
                }
            }

            // 开始处理这个响应
            this.processingTurns.add(turnId);
            this.processResponse(pendingResponse).finally(() => {
                this.processingTurns.delete(turnId);
            });

            // 检查并发限制
            if (this.sendingResponses.size >= this.config.maxConcurrentSends) {
                break;
            }
        }
    }

    /**
     * 处理单个响应
     */
    private async processResponse(response: QueuedResponse): Promise<void> {
        response.sendStatus = ResponseSendStatus.SENDING;
        this.sendingResponses.set(response.id, response);

        try {
            await this.performSend(response);
            response.sendStatus = ResponseSendStatus.SENT;
            response.sentAt = new Date();

            // 发射发送成功事件
            await this.eventBus.emitMiddlewareEvent<ResponseSentEvent>({
                id: Date.now(),
                type: "response_sent",
                timestamp: new Date(),
                turnId: response.turnId,
                responseId: response.id,
                success: true
            });

        } catch (error) {
            response.retryCount++;
            
            if (response.retryCount <= response.maxRetries) {
                // 重试
                response.sendStatus = ResponseSendStatus.PENDING;
                this.ctx.logger.warn(`响应发送失败，将重试 (${response.retryCount}/${response.maxRetries}):`, error);
            } else {
                // 达到最大重试次数
                response.sendStatus = ResponseSendStatus.FAILED;
                response.error = (error as Error).message;
                
                // 发射发送失败事件
                await this.eventBus.emitMiddlewareEvent<ResponseSentEvent>({
                    id: Date.now(),
                    type: "response_sent",
                    timestamp: new Date(),
                    turnId: response.turnId,
                    responseId: response.id,
                    success: false
                });

                this.ctx.logger.error(`响应发送最终失败 (responseId: ${response.id}):`, error);
            }
        } finally {
            this.sendingResponses.delete(response.id);
        }
    }

    /**
     * 执行实际的发送操作
     */
    private async performSend(response: QueuedResponse): Promise<void> {
        // 这里需要根据实际的平台适配器来实现发送逻辑
        // 暂时使用模拟实现
        
        // 获取Turn的上下文信息（这里需要从某个地方获取）
        // const turnContext = await this.getTurnContext(response.turnId);
        
        // 模拟发送延迟
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 根据响应类型进行不同的处理
        switch (response.type) {
            case ResponseType.THINKING:
                // 发送思考过程，可能需要特殊格式
                break;
            case ResponseType.INTERMEDIATE:
                // 发送中间结果
                break;
            case ResponseType.FINAL:
                // 发送最终答案
                break;
            case ResponseType.ERROR:
                // 发送错误信息
                break;
        }

        this.ctx.logger.debug(`响应发送成功 (responseId: ${response.id}, type: ${response.type})`);
    }

    /**
     * 服务停止时清理资源
     */
    async stop(): Promise<void> {
        if (this.processingTimer) {
            clearInterval(this.processingTimer);
            this.processingTimer = undefined;
        }

        // 取消所有待发送的响应
        for (const queue of this.responseQueues.values()) {
            queue.forEach(response => {
                if (response.sendStatus === ResponseSendStatus.PENDING) {
                    response.sendStatus = ResponseSendStatus.CANCELLED;
                }
            });
        }

        this.responseQueues.clear();
        this.processingTurns.clear();
        this.sendingResponses.clear();

        await super.stop();
    }
}
