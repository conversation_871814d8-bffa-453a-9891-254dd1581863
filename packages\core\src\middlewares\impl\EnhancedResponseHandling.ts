import { Context } from "koishi";
import { OutputProcessingMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { 
    ConversationState, 
    MiddlewareContext, 
    MiddlewarePriority,
    ResponseType,
    ExecutionState
} from "../core/MiddlewareCore";
import { EnhancedMiddlewareContext } from "../core/EnhancedMiddlewareContext";
import { MultiResponseManager } from "../core/MultiResponseManager";

export interface FunctionTool {
    function: string;
    params: Record<string, unknown>;
}

export interface EnhancedOutputFormat {
    thoughts: {
        observe: string;
        analyze_infer: string;
        plan: string;
    };
    actions: FunctionTool[];
    request_heartbeat: boolean;
    // 新增：多响应支持
    responses?: {
        thinking?: string;
        intermediate?: string[];
        final?: string;
    };
    // 新增：执行控制
    execution_control?: {
        pause?: boolean;
        pause_reason?: string;
        jump_to?: {
            phase: string;
            middleware?: string;
        };
        breakpoints?: Array<{
            phase: string;
            middleware?: string;
            condition?: string;
        }>;
    };
}

/**
 * 增强响应处理中间件配置
 */
export interface EnhancedResponseHandlingConfig {
    maxRetry: number;
    life: number;
    maxHeartbeat: number;
    enableToolValidation?: boolean;
    parallelExecution?: boolean;
    // 新增配置
    enableMultiResponse?: boolean;
    enableThinkingResponse?: boolean;
    thinkingResponseDelay?: number;
    intermediateResponseDelay?: number;
    enableExecutionControl?: boolean;
}

/**
 * 增强响应处理中间件
 * 支持多响应、执行控制和事件驱动通信
 */
@middleware({
    id: "builtin.enhanced-response-handling",
    name: "增强响应处理中间件",
    phase: "output_processing" as any,
    priority: MiddlewarePriority.HIGH,
    dependencies: ["builtin.llm-processing"],
})
@validateConfig<EnhancedResponseHandlingConfig>((config) => {
    if (config.maxRetry < 0 || config.maxRetry > 10) {
        return "maxRetry 必须在 0-10 之间";
    }
    if (config.life < 1 || config.life > 10) {
        return "life 必须在 1-10 之间";
    }
    return true;
})
export class EnhancedResponseHandlingMiddleware extends OutputProcessingMiddleware<EnhancedResponseHandlingConfig> {
    private multiResponseManager: MultiResponseManager;

    constructor(ctx: Context, config: EnhancedResponseHandlingConfig) {
        super(ctx, config);
        this.multiResponseManager = this.ctx["yesimbot.multiResponse"];
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 只在响应状态下执行
        if (ctx.state !== ConversationState.RESPONDING) {
            await next();
            return;
        }

        const startTime = Date.now();
        const enhancedCtx = ctx as EnhancedMiddlewareContext;

        try {
            // 1. 解析和验证响应
            const response = this.parseAndValidateResponse(ctx);
            if (!response) {
                this.logger.warn("LLM响应解析失败或无效，处理中止");
                await this.finalizeProcessing(enhancedCtx);
                return;
            }

            // 2. 处理执行控制指令
            if (this.config.enableExecutionControl && response.execution_control) {
                await this.handleExecutionControl(enhancedCtx, response.execution_control);
            }

            // 3. 处理多响应
            if (this.config.enableMultiResponse && response.responses) {
                await this.handleMultipleResponses(enhancedCtx, response.responses);
            }

            // 4. 记录思考过程
            this.logThoughts(response.thoughts);

            // 5. 处理动作和构建代理响应
            const agentResponse = await this.processActions(enhancedCtx, response.actions, response.thoughts);
            enhancedCtx.agentResponses.push(agentResponse);

            // 6. 保存代理响应到数据库
            await this.saveAgentResponse(enhancedCtx, agentResponse);

            // 7. 处理心跳请求
            if (response.request_heartbeat) {
                await this.handleHeartbeat(enhancedCtx);
            } else {
                await next();
            }
        } catch (error) {
            this.logger.error("处理LLM响应时发生错误:", error);
            throw error;
        } finally {
            await this.finalizeProcessing(enhancedCtx);
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 解析和验证响应
     */
    private parseAndValidateResponse(ctx: MiddlewareContext): EnhancedOutputFormat | null {
        try {
            if (!ctx.llmResponse?.text) {
                this.logger.warn("LLM响应为空");
                return null;
            }

            const response = JSON.parse(ctx.llmResponse.text) as EnhancedOutputFormat;

            // 验证响应结构
            if (!response.thoughts || !response.actions || typeof response.request_heartbeat !== "boolean") {
                this.logger.warn("LLM响应结构无效");
                return null;
            }

            return response;
        } catch (error) {
            this.logger.error("解析LLM响应失败:", error);
            return null;
        }
    }

    /**
     * 处理执行控制指令
     */
    private async handleExecutionControl(
        ctx: EnhancedMiddlewareContext, 
        control: NonNullable<EnhancedOutputFormat["execution_control"]>
    ): Promise<void> {
        // 处理暂停指令
        if (control.pause) {
            ctx.pause(control.pause_reason || "LLM请求暂停");
            return;
        }

        // 处理跳转指令
        if (control.jump_to) {
            const phase = this.parsePhase(control.jump_to.phase);
            if (phase) {
                ctx.jumpTo({
                    phase,
                    middlewareId: control.jump_to.middleware
                });
            }
        }

        // 处理断点设置
        if (control.breakpoints) {
            for (const bp of control.breakpoints) {
                const phase = this.parsePhase(bp.phase);
                if (phase) {
                    ctx.addBreakpoint({
                        phase,
                        middlewareId: bp.middleware,
                        condition: bp.condition ? this.parseCondition(bp.condition) : undefined,
                        action: "pause"
                    });
                }
            }
        }
    }

    /**
     * 处理多响应
     */
    private async handleMultipleResponses(
        ctx: EnhancedMiddlewareContext,
        responses: NonNullable<EnhancedOutputFormat["responses"]>
    ): Promise<void> {
        // 发送思考过程响应
        if (this.config.enableThinkingResponse && responses.thinking) {
            await ctx.sendThinking(responses.thinking);
            
            // 如果配置了延迟，等待一段时间
            if (this.config.thinkingResponseDelay) {
                await this.sleep(this.config.thinkingResponseDelay);
            }
        }

        // 发送中间结果响应
        if (responses.intermediate && responses.intermediate.length > 0) {
            for (const intermediate of responses.intermediate) {
                await ctx.sendIntermediate(intermediate);
                
                // 如果配置了延迟，等待一段时间
                if (this.config.intermediateResponseDelay) {
                    await this.sleep(this.config.intermediateResponseDelay);
                }
            }
        }

        // 最终响应将在后续处理中发送
        if (responses.final) {
            ctx.addResponse(ResponseType.FINAL, responses.final);
        }
    }

    /**
     * 记录思考过程
     */
    private logThoughts(thoughts: EnhancedOutputFormat["thoughts"]): void {
        if (this.config.enableToolValidation) {
            this.logger.debug("=== AI思考过程 ===");
            this.logger.debug(`观察: ${thoughts.observe}`);
            this.logger.debug(`分析推理: ${thoughts.analyze_infer}`);
            this.logger.debug(`计划: ${thoughts.plan}`);
            this.logger.debug("================");
        }
    }

    /**
     * 处理动作
     */
    private async processActions(
        ctx: EnhancedMiddlewareContext, 
        actions: FunctionTool[], 
        thoughts: EnhancedOutputFormat["thoughts"]
    ): Promise<any> {
        const actionResults: any[] = [];
        const executionPromises: Promise<void>[] = [];

        for (const [index, action] of actions.entries()) {
            const executeAction = async () => {
                // 执行工具的逻辑（简化实现）
                try {
                    const result = await this.executeTool(action, ctx);
                    actionResults[index] = result;
                    
                    this.logger.debug(
                        `工具 ${action.function} 执行成功 (耗时: ${result.duration || 0}ms)`
                    );
                } catch (error) {
                    actionResults[index] = {
                        status: "error",
                        error: (error as Error).message,
                        duration: 0,
                    };
                    
                    this.logger.error(`工具 ${action.function} 执行失败:`, error);
                }
            };

            if (this.config.parallelExecution) {
                executionPromises.push(executeAction());
            } else {
                await executeAction();
            }
        }

        // 等待并行执行完成
        if (this.config.parallelExecution) {
            await Promise.all(executionPromises);
        }

        // 构建代理响应对象
        return {
            thoughts,
            actions: actions.map((action, index) => ({
                function: action.function,
                params: action.params,
                result: actionResults[index],
            })),
            timestamp: new Date().toISOString(),
            executionTime: actionResults.reduce((sum, result) => sum + (result?.duration || 0), 0),
        };
    }

    /**
     * 执行工具（简化实现）
     */
    private async executeTool(tool: FunctionTool, ctx: EnhancedMiddlewareContext): Promise<any> {
        const startTime = Date.now();
        
        try {
            const toolManager = this.ctx["yesimbot.tool"];
            if (!toolManager) {
                throw new Error("工具管理器未找到");
            }

            const toolInstance = toolManager.getTool(tool.function);
            if (!toolInstance) {
                throw new Error(`工具 ${tool.function} 未找到`);
            }

            const result = await toolInstance.execute(tool.params, {
                koishiContext: ctx.koishiContext,
                koishiSession: ctx.koishiSession,
                platform: ctx.platform,
            });

            return {
                status: "success",
                result: result.result,
                duration: Date.now() - startTime,
            };
        } catch (error) {
            return {
                status: "error",
                error: (error as Error).message,
                duration: Date.now() - startTime,
            };
        }
    }

    /**
     * 保存代理响应
     */
    private async saveAgentResponse(ctx: EnhancedMiddlewareContext, agentResponse: any): Promise<void> {
        try {
            const dataManager = this.ctx["yesimbot.data"];
            if (dataManager && ctx.currentTurnId) {
                await dataManager.addAgentResponse(ctx.currentTurnId, agentResponse);
            }
        } catch (error) {
            this.logger.error("保存代理响应失败:", error);
        }
    }

    /**
     * 处理心跳请求
     */
    private async handleHeartbeat(ctx: EnhancedMiddlewareContext): Promise<void> {
        ctx.heartbeatCount++;

        if (ctx.heartbeatCount >= this.config.maxHeartbeat) {
            this.logger.info(`达到最大心跳次数 (${this.config.maxHeartbeat})，结束对话`);
            await this.finalizeProcessing(ctx);
            return;
        }

        this.logger.debug(`心跳请求 ${ctx.heartbeatCount}/${this.config.maxHeartbeat}`);

        // 暂停当前执行，等待下一轮心跳触发
        ctx.pause("心跳等待");
    }

    /**
     * 完成处理
     */
    private async finalizeProcessing(ctx: EnhancedMiddlewareContext): Promise<void> {
        try {
            await ctx.transitionTo(ConversationState.IDLE);
            ctx.metadata.executionState = ExecutionState.COMPLETED;
            await ctx.updateSharedContext("processingComplete", true, "response-handling");
        } catch (error) {
            this.logger.error("完成处理时发生错误:", error);
        }
    }

    // === 辅助方法 ===

    private parsePhase(phaseStr: string): any {
        // 简化实现，实际应该有完整的阶段解析逻辑
        const phaseMap: Record<string, any> = {
            "preprocessing": "preprocessing",
            "input_processing": "input_processing",
            "condition_check": "condition_check",
            "core_processing": "core_processing",
            "output_processing": "output_processing",
            "postprocessing": "postprocessing",
        };
        return phaseMap[phaseStr];
    }

    private parseCondition(conditionStr: string): ((ctx: MiddlewareContext) => boolean) | undefined {
        // 简化实现，实际应该有完整的条件解析逻辑
        return undefined;
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        try {
            const toolManager = this.ctx["yesimbot.tool"];
            const multiResponseManager = this.ctx["yesimbot.multiResponse"];
            return !!toolManager && !!multiResponseManager;
        } catch (error) {
            this.logger.error("增强响应处理健康检查失败:", error);
            return false;
        }
    }
}
