import { Random } from "koishi";

import { ChatMessage } from "../shared/database";
import { getChannelType } from "../shared/utils";
import { ConversationFlowAnalyzer } from "./ConversationFlowAnalyzer";
import { ReplyConditionConfig } from "../middlewares/impl/ReplyCondition";
import { MessageContext } from "../middlewares/core/MiddlewareCore";

// 回复决策结果
export interface ReplyDecision {
    shouldReply: boolean;
    strategy: string;
    confidence: number;
    waitTime: number;
    reason: string;
}

// 频道状态
export interface ChannelState {
    willingness: number;
    processing: boolean;
    lastMessageTime: number;
    lastMessageUser: string;
}

// 回复策略接口
export interface ReplyStrategy {
    name: string;
    enabled: boolean;
    evaluate(ctx: MessageContext, state: ChannelState): Promise<ReplyDecision>;
}

// @提及策略
export class AtMentionStrategy implements ReplyStrategy {
    name = "at_mention";
    enabled: boolean;

    constructor(private config: ReplyConditionConfig["Strategies"]["AtMention"]) {
        this.enabled = config.Enabled;
    }

    async evaluate(ctx: MessageContext): Promise<ReplyDecision> {
        if (!ctx.isMentioned) {
            return {
                shouldReply: false,
                strategy: this.name,
                confidence: 0,
                waitTime: 1000,
                reason: "not_mentioned",
            };
        }

        const shouldReply = Random.bool(this.config.Probability);

        return {
            shouldReply,
            strategy: this.name,
            confidence: shouldReply ? 1.0 : 0,
            waitTime: 1000, // 快速响应@消息
            reason: shouldReply ? "direct_mention" : "mention_probability_failed",
        };
    }
}

// 阈值策略
export class ThresholdStrategy implements ReplyStrategy {
    name = "threshold";
    enabled: boolean;

    constructor(private config: ReplyConditionConfig["Strategies"]["Threshold"]) {
        this.enabled = config.Enabled;
    }

    async evaluate(ctx: MessageContext, state: ChannelState): Promise<ReplyDecision> {
        const threshold = this.config.Value;
        const confidence = Math.min(state.willingness / threshold, 1.0);
        const shouldReply = state.willingness >= threshold;

        return {
            shouldReply,
            strategy: this.name,
            confidence,
            waitTime: 3000, // 标准等待时间
            reason: shouldReply ? "threshold_reached" : "threshold_not_reached",
        };
    }
}

// 对话流策略
export class ConversationFlowStrategy implements ReplyStrategy {
    name = "conversation_flow";
    enabled: boolean;

    constructor(private config: ReplyConditionConfig["Strategies"]["ConversationFlow"], private flowAnalyzer: ConversationFlowAnalyzer) {
        this.enabled = config.Enabled;
    }

    async evaluate(ctx: MessageContext): Promise<ReplyDecision> {
        const channelId = ctx.koishiSession.channelId;
        const userId = ctx.koishiSession.author.id;

        // 构造消息对象
        const message: ChatMessage = {
            messageId: ctx.koishiSession.messageId,
            content: ctx.koishiSession.content,
            sender: {
                id: userId,
                name: ctx.koishiSession.author.name || ctx.koishiSession.author.nick,
                nick: ctx.koishiSession.author.nick,
            },
            timestamp: new Date(ctx.koishiSession.timestamp),
            channel: { id: channelId, type: getChannelType(channelId) },
        };

        // 分析对话流
        await this.flowAnalyzer.analyzeMessage(channelId, message);
        const flowDecision = this.flowAnalyzer.shouldReply(channelId, message);

        const shouldReply = flowDecision.shouldReply && flowDecision.confidence >= this.config.ConfidenceThreshold;

        return {
            shouldReply,
            strategy: this.name,
            confidence: flowDecision.confidence,
            waitTime: flowDecision.suggestedWaitTime || 3000,
            reason: flowDecision.reason,
        };
    }
}

// 简化的意愿值服务
export class WillingnessService {
    private channelWillingness = new Map<string, number>();

    constructor(private config?: ReplyConditionConfig["Advanced"]["Willingness"]) {}

    updateWillingness(channelId: string, isMentioned: boolean, messageContent: string): void {
        if (!this.config) return;

        const current = this.channelWillingness.get(channelId) || 0;

        // 基础增加量
        let increase = isMentioned ? this.config.AtIncrease : this.config.MessageIncrease;

        // 关键词检测 - 每匹配一个关键词增加额外意愿值
        if (this.config.Keywords && messageContent) {
            const lowerContent = messageContent.toLowerCase();
            const matchedKeywords = this.config.Keywords.List.filter((keyword) => lowerContent.includes(keyword.toLowerCase()));
            if (matchedKeywords.length > 0) {
                increase += this.config.Keywords.Increase * matchedKeywords.length;
            }
        }

        const newWillingness = Math.max(0, current + increase);
        this.channelWillingness.set(channelId, newWillingness);
    }

    getWillingness(channelId: string): number {
        return this.channelWillingness.get(channelId) || 0;
    }

    resetAfterReply(channelId: string): void {
        if (!this.config) return;

        const current = this.getWillingness(channelId);
        const retained = current * this.config.RetentionAfterReply;
        this.channelWillingness.set(channelId, retained);
    }

    decay(): void {
        if (!this.config) return;

        for (const [channelId, willingness] of this.channelWillingness) {
            const decayed = Math.max(0, willingness - this.config.DecayRate);
            this.channelWillingness.set(channelId, decayed);
        }
    }
}
