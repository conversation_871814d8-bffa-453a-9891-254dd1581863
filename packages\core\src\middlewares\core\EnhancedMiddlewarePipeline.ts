import { Context } from "koishi";
import { 
    IMiddleware, 
    MiddlewarePhase, 
    MiddlewareStatus,
    ExecutionState,
    MessageContext
} from "./MiddlewareCore";
import { EnhancedMiddlewareContext } from "./EnhancedMiddlewareContext";
import { MiddlewareEventBus } from "../../shared/events/MiddlewareEventBus";
import { MiddlewareRegistry } from "../registry/MiddlewareRegistry";
import { 
    MiddlewareStartedEvent,
    MiddlewareCompletedEvent,
    MiddlewareFailedEvent,
    MiddlewareSkippedEvent
} from "../../shared/events/types";

/**
 * 执行状态快照
 */
interface ExecutionSnapshot {
    phase: MiddlewarePhase;
    middlewareIndex: number;
    context: any; // 序列化的上下文状态
    timestamp: Date;
}

/**
 * 增强的中间件管道
 * 支持暂停、恢复、跳转执行和多响应机制
 */
export class EnhancedMiddlewarePipeline {
    private middlewares: IMiddleware[] = [];
    private phaseMap = new Map<MiddlewarePhase, IMiddleware[]>();
    private isBuilt = false;
    private executionSnapshots: ExecutionSnapshot[] = [];
    private maxSnapshots = 10;

    constructor(
        private ctx: Context, 
        private registry: MiddlewareRegistry,
        private eventBus: MiddlewareEventBus
    ) {}

    /**
     * 添加中间件到管道
     */
    add(middlewareId: string, config?: any): this {
        const middleware = this.registry.create(middlewareId, config);
        this.middlewares.push(middleware);
        this.isBuilt = false;
        return this;
    }

    /**
     * 构建执行管道
     */
    async build(): Promise<void> {
        if (this.isBuilt) return;

        // 1. 依赖检查和排序
        this.validateDependencies();
        this.sortMiddlewares();

        // 2. 按阶段分组
        this.groupByPhase();

        // 3. 初始化所有中间件
        await this.initializeMiddlewares();

        this.isBuilt = true;
        this.ctx.logger.info(`增强中间件管道构建完成，共 ${this.middlewares.length} 个中间件`);
    }

    /**
     * 执行中间件管道
     */
    async execute(messageContext: MessageContext): Promise<void> {
        if (!this.isBuilt) {
            throw new Error("中间件管道未构建，请先调用 build()");
        }

        const ctx = this.createEnhancedContext(messageContext);
        ctx.metadata.executionState = ExecutionState.RUNNING;

        try {
            await this.executeWithControl(ctx);
        } catch (error) {
            ctx.fail(error as Error, "pipeline");
            throw error;
        } finally {
            ctx.dispose();
        }
    }

    /**
     * 从指定位置恢复执行
     */
    async resumeFrom(
        messageContext: MessageContext, 
        phase: MiddlewarePhase, 
        middlewareId?: string
    ): Promise<void> {
        if (!this.isBuilt) {
            throw new Error("中间件管道未构建，请先调用 build()");
        }

        const ctx = this.createEnhancedContext(messageContext);
        ctx.metadata.currentPhase = phase;
        ctx.metadata.executionState = ExecutionState.RUNNING;

        try {
            await this.executeFromPhase(ctx, phase, middlewareId);
        } catch (error) {
            ctx.fail(error as Error, "pipeline");
            throw error;
        } finally {
            ctx.dispose();
        }
    }

    /**
     * 创建增强的中间件上下文
     */
    private createEnhancedContext(messageContext: MessageContext): EnhancedMiddlewareContext {
        return new EnhancedMiddlewareContext(messageContext, this.eventBus);
    }

    /**
     * 带控制的执行
     */
    private async executeWithControl(ctx: EnhancedMiddlewareContext): Promise<void> {
        const phases = Object.values(MiddlewarePhase);

        for (const phase of phases) {
            // 检查是否应该暂停
            if (ctx.shouldPause()) {
                this.ctx.logger.info(`执行在阶段 ${phase} 暂停: ${ctx.metadata.pauseReason}`);
                return;
            }

            ctx.metadata.currentPhase = phase;
            const phaseMiddlewares = this.phaseMap.get(phase) || [];

            if (phaseMiddlewares.length > 0) {
                await this.executePhaseMiddlewares(ctx, phaseMiddlewares);
            }

            // 创建执行快照
            this.createSnapshot(ctx, phase, 0);
        }

        ctx.metadata.executionState = ExecutionState.COMPLETED;
    }

    /**
     * 从指定阶段开始执行
     */
    private async executeFromPhase(
        ctx: EnhancedMiddlewareContext, 
        startPhase: MiddlewarePhase, 
        startMiddlewareId?: string
    ): Promise<void> {
        const phases = Object.values(MiddlewarePhase);
        const startIndex = phases.indexOf(startPhase);
        
        if (startIndex === -1) {
            throw new Error(`无效的阶段: ${startPhase}`);
        }

        for (let i = startIndex; i < phases.length; i++) {
            const phase = phases[i];
            
            // 检查是否应该暂停
            if (ctx.shouldPause()) {
                this.ctx.logger.info(`执行在阶段 ${phase} 暂停: ${ctx.metadata.pauseReason}`);
                return;
            }

            ctx.metadata.currentPhase = phase;
            const phaseMiddlewares = this.phaseMap.get(phase) || [];

            if (phaseMiddlewares.length > 0) {
                // 如果指定了起始中间件，找到对应的索引
                let startMiddlewareIndex = 0;
                if (i === startIndex && startMiddlewareId) {
                    startMiddlewareIndex = phaseMiddlewares.findIndex(m => m.id === startMiddlewareId);
                    if (startMiddlewareIndex === -1) {
                        this.ctx.logger.warn(`未找到中间件 ${startMiddlewareId}，从阶段开始执行`);
                        startMiddlewareIndex = 0;
                    }
                }

                await this.executePhaseMiddlewares(ctx, phaseMiddlewares, startMiddlewareIndex);
            }

            // 创建执行快照
            this.createSnapshot(ctx, phase, 0);
        }

        ctx.metadata.executionState = ExecutionState.COMPLETED;
    }

    /**
     * 执行特定阶段的中间件
     */
    private async executePhaseMiddlewares(
        ctx: EnhancedMiddlewareContext, 
        middlewares: IMiddleware[],
        startIndex: number = 0
    ): Promise<void> {
        let index = startIndex;

        const next = async (): Promise<void> => {
            if (index >= middlewares.length) return;

            // 检查是否应该暂停
            if (ctx.shouldPause()) {
                return;
            }

            const middleware = middlewares[index++];
            
            if (!middleware.enabled) {
                ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.SKIPPED);
                await this.emitMiddlewareEvent(ctx, "skipped", middleware);
                return next();
            }

            const startTime = Date.now();
            ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.RUNNING);
            
            // 发射开始事件
            await this.emitMiddlewareEvent(ctx, "started", middleware);

            try {
                await middleware.execute(ctx, next);

                const duration = Date.now() - startTime;
                ctx.metadata.performance.set(middleware.id, duration);
                ctx.metadata.executedMiddlewares.push(middleware.id);
                ctx.metadata.middlewareStates.set(middleware.id, MiddlewareStatus.COMPLETED);
                
                // 发射完成事件
                await this.emitMiddlewareEvent(ctx, "completed", middleware, { duration });
            } catch (error) {
                ctx.fail(error as Error, middleware.id);
                
                // 发射失败事件
                await this.emitMiddlewareEvent(ctx, "failed", middleware, { error: (error as Error).message });
                throw error;
            }
        };

        await next();
    }

    /**
     * 发射中间件事件
     */
    private async emitMiddlewareEvent(
        ctx: EnhancedMiddlewareContext,
        eventType: "started" | "completed" | "failed" | "skipped",
        middleware: IMiddleware,
        extra?: any
    ): Promise<void> {
        const baseEvent = {
            id: Date.now(),
            timestamp: new Date(),
            middlewareId: middleware.id,
            middlewareName: middleware.name,
            phase: middleware.phase,
            turnId: ctx.currentTurnId
        };

        switch (eventType) {
            case "started":
                await ctx.emitEvent<MiddlewareStartedEvent>({
                    ...baseEvent,
                    type: "middleware_started"
                });
                break;
            case "completed":
                await ctx.emitEvent<MiddlewareCompletedEvent>({
                    ...baseEvent,
                    type: "middleware_completed",
                    duration: extra?.duration || 0
                });
                break;
            case "failed":
                await ctx.emitEvent<MiddlewareFailedEvent>({
                    ...baseEvent,
                    type: "middleware_failed",
                    error: extra?.error || "未知错误"
                });
                break;
            case "skipped":
                await ctx.emitEvent<MiddlewareSkippedEvent>({
                    ...baseEvent,
                    type: "middleware_skipped",
                    reason: extra?.reason
                });
                break;
        }
    }

    /**
     * 创建执行快照
     */
    private createSnapshot(ctx: EnhancedMiddlewareContext, phase: MiddlewarePhase, middlewareIndex: number): void {
        const snapshot: ExecutionSnapshot = {
            phase,
            middlewareIndex,
            context: this.serializeContext(ctx),
            timestamp: new Date()
        };

        this.executionSnapshots.push(snapshot);

        // 限制快照数量
        if (this.executionSnapshots.length > this.maxSnapshots) {
            this.executionSnapshots = this.executionSnapshots.slice(-this.maxSnapshots);
        }
    }

    /**
     * 序列化上下文（简化实现）
     */
    private serializeContext(ctx: EnhancedMiddlewareContext): any {
        return {
            state: ctx.state,
            heartbeatCount: ctx.heartbeatCount,
            responses: ctx.responses,
            shared: Object.fromEntries(ctx.shared),
            metadata: {
                ...ctx.metadata,
                middlewareStates: Object.fromEntries(ctx.metadata.middlewareStates),
                performance: Object.fromEntries(ctx.metadata.performance)
            }
        };
    }

    // === 其他辅助方法 ===

    private validateDependencies(): void {
        const middlewareIds = new Set(this.middlewares.map((m) => m.id));

        for (const middleware of this.middlewares) {
            for (const dep of middleware.dependencies) {
                if (!middlewareIds.has(dep)) {
                    throw new Error(`中间件 ${middleware.id} 依赖的中间件 ${dep} 未找到`);
                }
            }
        }
    }

    private sortMiddlewares(): void {
        this.middlewares.sort((a, b) => {
            if (a.phase !== b.phase) {
                return Object.values(MiddlewarePhase).indexOf(a.phase) - Object.values(MiddlewarePhase).indexOf(b.phase);
            }
            return b.priority - a.priority;
        });
    }

    private groupByPhase(): void {
        this.phaseMap.clear();

        for (const middleware of this.middlewares) {
            const phaseMiddlewares = this.phaseMap.get(middleware.phase) || [];
            phaseMiddlewares.push(middleware);
            this.phaseMap.set(middleware.phase, phaseMiddlewares);
        }
    }

    private async initializeMiddlewares(): Promise<void> {
        for (const middleware of this.middlewares) {
            if (middleware.initialize) {
                try {
                    await middleware.initialize();
                } catch (error) {
                    this.ctx.logger.error(`初始化中间件 ${middleware.id} 失败:`, error);
                    throw error;
                }
            }
        }
    }

    /**
     * 获取执行快照
     */
    getExecutionSnapshots(): ExecutionSnapshot[] {
        return [...this.executionSnapshots];
    }

    /**
     * 清理管道
     */
    async dispose(): Promise<void> {
        for (const middleware of this.middlewares) {
            if (middleware.dispose) {
                try {
                    await middleware.dispose();
                } catch (error) {
                    this.ctx.logger.error(`清理中间件 ${middleware.id} 失败:`, error);
                }
            }
        }
        this.middlewares = [];
        this.phaseMap.clear();
        this.executionSnapshots = [];
        this.isBuilt = false;
    }
}
