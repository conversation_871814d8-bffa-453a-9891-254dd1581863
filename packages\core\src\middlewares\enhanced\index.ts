/**
 * 增强中间件功能导出
 * 
 * 本模块提供了增强的中间件架构功能，包括：
 * - 执行控制（暂停、恢复、跳转）
 * - 多响应支持（思考过程、中间结果、最终答案）
 * - 事件驱动通信（异步协调、状态同步）
 */

// === 核心类型和枚举 ===
export {
    MiddlewarePhase,
    MiddlewarePriority,
    MiddlewareStatus,
    ExecutionState,
    ResponseType,
    ConversationState
} from "../core/MiddlewareCore";

export type {
    IMiddleware,
    MiddlewareContext,
    MiddlewareInfo,
    MiddlewareDefinition,
    ResponseItem,
    ExecutionBreakpoint
} from "../core/MiddlewareCore";

// === 事件系统 ===
export { MiddlewareEventBus } from "../../shared/events/MiddlewareEventBus";

export {
    EVENT_NAMES
} from "../../shared/events/types";

export type {
    AllEvents,
    MiddlewareEvent,
    ChannelEvent,
    EventHandler,
    EventEmitter,
    MiddlewareEventBus as IMiddlewareEventBus,
    // 中间件执行事件
    MiddlewareStartedEvent,
    MiddlewareCompletedEvent,
    MiddlewareFailedEvent,
    MiddlewareSkippedEvent,
    // 执行控制事件
    ExecutionPausedEvent,
    ExecutionResumedEvent,
    ExecutionJumpedEvent,
    // 响应事件
    ResponseGeneratedEvent,
    ResponseQueuedEvent,
    ResponseSentEvent,
    // 状态同步事件
    StateChangedEvent,
    ContextUpdatedEvent
} from "../../shared/events/types";

// === 增强上下文 ===
export { EnhancedMiddlewareContext } from "../core/EnhancedMiddlewareContext";

// === 增强执行引擎 ===
export { EnhancedMiddlewarePipeline } from "../core/EnhancedMiddlewarePipeline";

// === 多响应管理 ===
export { 
    MultiResponseManager,
    ResponseSendStatus
} from "../core/MultiResponseManager";

export type {
    QueuedResponse,
    ResponseSendConfig
} from "../core/MultiResponseManager";

// === 增强中间件实现 ===
export { EnhancedResponseHandlingMiddleware } from "../impl/EnhancedResponseHandling";

export type {
    EnhancedOutputFormat,
    EnhancedResponseHandlingConfig
} from "../impl/EnhancedResponseHandling";

// === 工具函数 ===

/**
 * 创建增强中间件管道的工厂函数
 */
export function createEnhancedPipeline(
    ctx: any, 
    registry: any, 
    eventBus: MiddlewareEventBus
): EnhancedMiddlewarePipeline {
    return new EnhancedMiddlewarePipeline(ctx, registry, eventBus);
}

/**
 * 创建多响应管理器的工厂函数
 */
export function createMultiResponseManager(
    ctx: any, 
    eventBus: MiddlewareEventBus
): MultiResponseManager {
    return new MultiResponseManager(ctx, eventBus);
}

/**
 * 创建事件总线的工厂函数
 */
export function createEventBus(ctx: any): MiddlewareEventBus {
    return new MiddlewareEventBus(ctx);
}

/**
 * 增强中间件配置选项
 */
export interface EnhancedMiddlewareOptions {
    /** 是否启用执行控制功能 */
    enableExecutionControl?: boolean;
    /** 是否启用多响应功能 */
    enableMultiResponse?: boolean;
    /** 是否启用事件驱动通信 */
    enableEventCommunication?: boolean;
    /** 响应队列最大大小 */
    maxResponseQueueSize?: number;
    /** 事件历史最大记录数 */
    maxEventHistory?: number;
    /** 执行快照最大数量 */
    maxExecutionSnapshots?: number;
}

/**
 * 默认的增强中间件配置
 */
export const DEFAULT_ENHANCED_OPTIONS: Required<EnhancedMiddlewareOptions> = {
    enableExecutionControl: true,
    enableMultiResponse: true,
    enableEventCommunication: true,
    maxResponseQueueSize: 50,
    maxEventHistory: 1000,
    maxExecutionSnapshots: 10,
};

/**
 * 增强中间件管理器
 * 统一管理所有增强功能组件
 */
export class EnhancedMiddlewareManager {
    public readonly eventBus: MiddlewareEventBus;
    public readonly multiResponseManager: MultiResponseManager;
    public readonly pipeline: EnhancedMiddlewarePipeline;

    constructor(
        private ctx: any,
        private registry: any,
        private options: EnhancedMiddlewareOptions = {}
    ) {
        const config = { ...DEFAULT_ENHANCED_OPTIONS, ...options };

        // 初始化事件总线
        this.eventBus = new MiddlewareEventBus(ctx);

        // 初始化多响应管理器
        this.multiResponseManager = new MultiResponseManager(ctx, this.eventBus);

        // 初始化增强管道
        this.pipeline = new EnhancedMiddlewarePipeline(ctx, registry, this.eventBus);

        this.setupIntegration(config);
    }

    /**
     * 设置组件间的集成
     */
    private setupIntegration(config: Required<EnhancedMiddlewareOptions>): void {
        // 监听管道执行事件，自动管理响应队列
        if (config.enableMultiResponse) {
            this.eventBus.onMiddlewareEvent("execution_paused", {
                handle: async (event) => {
                    // 当执行暂停时，可以处理待发送的响应
                    this.ctx.logger.debug(`执行暂停，处理响应队列: ${event.turnId}`);
                }
            });
        }

        // 监听响应事件，提供统计信息
        if (config.enableEventCommunication) {
            this.eventBus.onMiddlewareEvent("response_sent", {
                handle: (event) => {
                    this.ctx.logger.debug(`响应发送${event.success ? '成功' : '失败'}: ${event.responseId}`);
                }
            });
        }
    }

    /**
     * 执行增强中间件管道
     */
    async execute(messageContext: any): Promise<void> {
        return await this.pipeline.execute(messageContext);
    }

    /**
     * 从指定位置恢复执行
     */
    async resumeFrom(
        messageContext: any, 
        phase: any, 
        middlewareId?: string
    ): Promise<void> {
        return await this.pipeline.resumeFrom(messageContext, phase, middlewareId);
    }

    /**
     * 获取系统状态
     */
    getSystemStatus(): {
        eventBus: {
            listenerCount: Record<string, number>;
            eventHistorySize: number;
        };
        multiResponse: {
            activeQueues: number;
            totalPendingResponses: number;
        };
        pipeline: {
            isBuilt: boolean;
            middlewareCount: number;
            snapshotCount: number;
        };
    } {
        return {
            eventBus: {
                listenerCount: this.eventBus.getListenerStats(),
                eventHistorySize: this.eventBus.getEventHistory().length,
            },
            multiResponse: {
                activeQueues: 0, // 需要实现
                totalPendingResponses: 0, // 需要实现
            },
            pipeline: {
                isBuilt: true, // 需要从pipeline获取
                middlewareCount: 0, // 需要从pipeline获取
                snapshotCount: this.pipeline.getExecutionSnapshots().length,
            }
        };
    }

    /**
     * 清理资源
     */
    async dispose(): Promise<void> {
        await this.pipeline.dispose();
        await this.multiResponseManager.stop();
        await this.eventBus.stop();
    }
}

/**
 * 创建增强中间件管理器的便捷函数
 */
export function createEnhancedMiddlewareManager(
    ctx: any,
    registry: any,
    options?: EnhancedMiddlewareOptions
): EnhancedMiddlewareManager {
    return new EnhancedMiddlewareManager(ctx, registry, options);
}

// === 类型守卫和工具函数 ===

/**
 * 检查上下文是否为增强上下文
 */
export function isEnhancedContext(ctx: any): ctx is EnhancedMiddlewareContext {
    return ctx && 
           typeof ctx.pause === 'function' && 
           typeof ctx.resume === 'function' && 
           typeof ctx.addResponse === 'function';
}

/**
 * 检查事件是否为中间件事件
 */
export function isMiddlewareEvent(event: any): event is MiddlewareEvent {
    return event && 
           typeof event.type === 'string' && 
           event.type.startsWith('middleware_') || 
           event.type.startsWith('execution_') || 
           event.type.startsWith('response_') || 
           event.type.startsWith('state_') || 
           event.type.startsWith('context_');
}

/**
 * 格式化执行状态为可读字符串
 */
export function formatExecutionState(state: ExecutionState): string {
    const stateMap = {
        [ExecutionState.IDLE]: "空闲",
        [ExecutionState.RUNNING]: "运行中",
        [ExecutionState.PAUSED]: "已暂停",
        [ExecutionState.COMPLETED]: "已完成",
        [ExecutionState.FAILED]: "已失败",
        [ExecutionState.CANCELLED]: "已取消",
    };
    return stateMap[state] || "未知状态";
}

/**
 * 格式化响应类型为可读字符串
 */
export function formatResponseType(type: ResponseType): string {
    const typeMap = {
        [ResponseType.THINKING]: "思考过程",
        [ResponseType.INTERMEDIATE]: "中间结果",
        [ResponseType.FINAL]: "最终答案",
        [ResponseType.ERROR]: "错误信息",
    };
    return typeMap[type] || "未知类型";
}
