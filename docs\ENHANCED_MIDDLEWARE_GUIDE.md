# 增强中间件架构指南

本指南介绍了 YesImBot 2.0 中新增的增强中间件功能，包括执行控制、多响应支持和事件驱动通信。

## 🚀 新功能概览

### 1. 执行控制
- **暂停/恢复执行**：支持在任意中间件位置暂停和恢复执行
- **跳转执行**：可以跳转到指定的阶段或中间件
- **断点机制**：支持设置条件断点，实现灵活的执行控制
- **执行状态管理**：完整的执行状态跟踪和管理

### 2. 多响应支持
- **响应类型**：支持思考过程、中间结果、最终答案等不同类型的响应
- **响应队列**：智能的响应队列管理，支持优先级和延迟发送
- **流式响应**：支持实时输出中间处理结果
- **响应统计**：完整的响应状态跟踪和统计

### 3. 事件驱动通信
- **中间件事件**：丰富的中间件执行事件
- **异步通信**：支持中间件间的异步事件通信
- **状态同步**：通过事件实现状态一致性
- **事件历史**：完整的事件历史记录和查询

## 📋 核心组件

### EnhancedMiddlewareContext
增强的中间件上下文，提供执行控制和多响应功能：

```typescript
// 执行控制
ctx.pause("需要用户确认");
ctx.resume();
ctx.jumpTo({ phase: MiddlewarePhase.OUTPUT_PROCESSING });

// 断点管理
ctx.addBreakpoint({
    phase: MiddlewarePhase.CORE_PROCESSING,
    action: "pause",
    condition: (ctx) => ctx.heartbeatCount > 3
});

// 多响应
await ctx.sendThinking("让我思考一下...");
await ctx.sendIntermediate("找到了一些信息");
await ctx.sendFinal("基于分析，我的建议是...");

// 事件通信
ctx.onEvent("custom_event", (event) => {
    console.log("收到自定义事件:", event);
});
await ctx.emitEvent({ type: "custom_event", data: "test" });
```

### EnhancedMiddlewarePipeline
支持暂停/恢复的中间件执行引擎：

```typescript
const pipeline = new EnhancedMiddlewarePipeline(ctx, registry, eventBus);

// 正常执行
await pipeline.execute(messageContext);

// 从指定位置恢复执行
await pipeline.resumeFrom(
    messageContext, 
    MiddlewarePhase.CORE_PROCESSING, 
    "specific-middleware-id"
);
```

### MultiResponseManager
多响应管理器，处理响应队列和发送：

```typescript
const manager = new MultiResponseManager(ctx, eventBus);

// 添加响应到队列
await manager.queueResponse(
    turnId,
    ResponseType.THINKING,
    "思考中...",
    { priority: 5, delay: 1000 }
);

// 查看队列状态
const stats = manager.getResponseStats(turnId);
console.log(`待发送: ${stats.pending}, 已发送: ${stats.sent}`);
```

### MiddlewareEventBus
事件总线，支持中间件间通信：

```typescript
const eventBus = new MiddlewareEventBus(ctx);

// 监听中间件事件
eventBus.onMiddlewareEvent("middleware_completed", {
    handle: (event) => {
        console.log(`中间件 ${event.middlewareName} 执行完成`);
    }
});

// 等待特定事件
const event = await eventBus.waitForEvent(
    "execution_paused",
    (e) => e.turnId === currentTurnId,
    30000
);
```

## 🔧 使用示例

### 基本多响应处理

```typescript
export class MultiResponseMiddleware extends BaseMiddleware {
    async execute(ctx: EnhancedMiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 发送思考过程
        await ctx.sendThinking("正在分析您的问题...");
        
        // 执行分析
        const analysis = await this.analyzeInput(ctx);
        
        // 发送中间结果
        await ctx.sendIntermediate(`分析完成，找到 ${analysis.results.length} 个相关结果`);
        
        // 继续处理
        await next();
        
        // 发送最终结果
        await ctx.sendFinal(this.formatFinalResponse(analysis));
    }
}
```

### 条件执行控制

```typescript
export class ConditionalControlMiddleware extends BaseMiddleware {
    async execute(ctx: EnhancedMiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 检查是否需要用户确认
        if (this.needsUserConfirmation(ctx)) {
            // 暂停执行，等待用户响应
            ctx.pause("等待用户确认");
            
            // 发送确认请求
            await ctx.sendIntermediate("请确认是否继续执行此操作？");
            
            // 等待确认事件
            const confirmEvent = await ctx.waitForEvent("user_confirmed", 
                (e) => e.turnId === ctx.currentTurnId, 
                60000
            );
            
            if (confirmEvent.confirmed) {
                ctx.resume();
                await next();
            } else {
                ctx.addResponse(ResponseType.FINAL, "操作已取消");
            }
        } else {
            await next();
        }
    }
}
```

### 事件驱动协调

```typescript
export class EventCoordinatorMiddleware extends BaseMiddleware {
    async execute(ctx: EnhancedMiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 监听其他中间件的完成事件
        ctx.onEvent("data_processing_complete", async (event) => {
            await ctx.sendIntermediate(`数据处理完成: ${event.summary}`);
        });
        
        // 发射开始事件
        await ctx.emitEvent({
            type: "coordination_started",
            turnId: ctx.currentTurnId,
            timestamp: new Date()
        });
        
        await next();
        
        // 发射完成事件
        await ctx.emitEvent({
            type: "coordination_complete",
            turnId: ctx.currentTurnId,
            timestamp: new Date()
        });
    }
}
```

## 🎯 最佳实践

### 1. 执行控制
- 合理使用暂停机制，避免长时间阻塞
- 设置适当的超时时间
- 使用断点进行调试和流程控制

### 2. 多响应管理
- 根据响应类型设置合适的优先级
- 控制响应频率，避免刷屏
- 使用延迟发送来改善用户体验

### 3. 事件通信
- 使用有意义的事件名称
- 避免事件循环依赖
- 及时清理事件监听器

### 4. 性能优化
- 监控执行时间和资源使用
- 使用事件历史进行问题诊断
- 定期清理过期的响应队列

## 🔍 调试和监控

### 事件监控
```typescript
// 监听所有中间件事件
eventBus.onMiddlewareEvent("middleware_started", { handle: logEvent });
eventBus.onMiddlewareEvent("middleware_completed", { handle: logEvent });
eventBus.onMiddlewareEvent("middleware_failed", { handle: logEvent });

// 查看事件历史
const history = eventBus.getEventHistory("middleware_completed", 50);
console.log("最近50个完成事件:", history);
```

### 响应队列监控
```typescript
// 定期检查队列状态
setInterval(() => {
    const stats = multiResponseManager.getResponseStats(turnId);
    if (stats.failed > 0) {
        console.warn(`发现 ${stats.failed} 个失败的响应`);
    }
}, 5000);
```

### 执行状态监控
```typescript
// 监控执行状态变化
eventBus.onMiddlewareEvent("execution_paused", {
    handle: (event) => {
        console.log(`执行暂停: ${event.reason}`);
        // 可以在这里实现自动恢复逻辑
    }
});
```

## 📚 API 参考

详细的 API 文档请参考：
- [EnhancedMiddlewareContext API](./api/EnhancedMiddlewareContext.md)
- [MultiResponseManager API](./api/MultiResponseManager.md)
- [MiddlewareEventBus API](./api/MiddlewareEventBus.md)
- [EnhancedMiddlewarePipeline API](./api/EnhancedMiddlewarePipeline.md)

## 🚨 注意事项

1. **向后兼容性**：新功能完全向后兼容，现有中间件无需修改即可使用
2. **资源管理**：注意及时清理事件监听器和响应队列，避免内存泄漏
3. **错误处理**：增强的功能可能引入新的错误场景，需要完善错误处理
4. **测试覆盖**：使用提供的测试工具确保功能正确性

## 🔄 迁移指南

从旧版本迁移到增强中间件架构：

1. **保持现有中间件不变**：现有中间件可以继续使用
2. **逐步采用新功能**：可以选择性地在新中间件中使用增强功能
3. **更新配置**：根据需要更新中间件配置以启用新功能
4. **测试验证**：充分测试确保功能正常

通过这些增强功能，YesImBot 2.0 的中间件架构变得更加灵活和强大，能够支持更复杂的对话场景和交互模式。
