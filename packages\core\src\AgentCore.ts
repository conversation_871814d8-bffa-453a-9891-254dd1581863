import { Context } from "koishi";
import { Config } from "./config";
import { MessageContext, MiddlewareManager } from "./middlewares/base";
import { MiddlewareConfigurator } from "./services/MiddlewareConfigurator";
import { ServiceManager } from "./services/ServiceManager";
import { DataManager } from "./services/worldstate/DataManager";
import {
    ChatMessage,
    IMAGE_TABLE,
    INTERACTION_TABLE,
    ImageData,
    Interaction,
    LAST_REPLY_TABLE,
    MEMORY_TABLE,
    MESSAGE_TABLE,
    MemoryBlockData,
} from "./shared";

declare module "koishi" {
    interface Tables {
        [MESSAGE_TABLE]: ChatMessage;
        [MEMORY_TABLE]: MemoryBlockData;
        [INTERACTION_TABLE]: Interaction;
        [LAST_REPLY_TABLE]: {
            channelId: string;
            timestamp: Date;
        };
        [IMAGE_TABLE]: ImageData;
    }
}

/**
 * 简化的 Agent 实现
 * 减少抽象层次，使用更直观的依赖管理
 */
export class AgentCore {
    private services: ServiceManager;
    private middlewareManager: MiddlewareManager;
    private databaseManager: DataManager;

    constructor(private ctx: Context, private config: Config) {
        // 初始化组件
        this.services = new ServiceManager(ctx, config);
        this.databaseManager = new DataManager(ctx);
        this.middlewareManager = new MiddlewareManager(ctx);

        // 监听 Koishi 就绪事件
        ctx.on("ready", async () => {
            await this.initialize();
        });
    }

    /**
     * 初始化 Agent
     */
    private async initialize(): Promise<void> {
        try {
            this.ctx.logger.info("[SimpleAgent] 开始初始化...");

            // 1. 注册数据库表
            this.databaseManager.registerTables();

            // 2. 配置中间件
            await this.configureMiddlewares();

            // 3. 注册消息处理器
            this.registerMessageHandler();

            this.ctx.logger.info("[SimpleAgent] 初始化完成");
        } catch (error) {
            this.ctx.logger.error("[SimpleAgent] 初始化失败:", error);
            throw error;
        }
    }

    /**
     * 配置中间件
     */
    private async configureMiddlewares(): Promise<void> {
        const configurator = new MiddlewareConfigurator(this.ctx, this.config, this.services);

        // 使用传统的中间件管理器（向后兼容）
        const legacyManager = configurator.configure();

        // 这里可以逐步迁移到新的中间件系统
        // 目前保持向后兼容
        this.middlewareManager = legacyManager as any;
    }

    /**
     * 注册消息处理器
     */
    private registerMessageHandler(): void {
        this.ctx.middleware(async (session, next) => {
            try {
                // 检查频道权限
                const allowedChannels = this.config.ReplyCondition.Channels.find((slots) => slots.includes(session.channelId)) || [];

                if (allowedChannels.length === 0) {
                    if (this.config.Debug.EnableDebug) {
                        this.ctx.logger.info(`频道 ${session.channelId} 不在回复列表中，已跳过`);
                    }
                    return next();
                }

                // 创建消息上下文
                const messageContext = await MessageContext.create(this.ctx, session, allowedChannels);

                // 执行中间件链
                await this.middlewareManager.execute(messageContext);

                return next();
            } catch (error) {
                this.ctx.logger.error("[SimpleAgent] 消息处理错误:", (error as Error).message);
                if (this.config.Debug.EnableDebug) {
                    this.ctx.logger.error((error as Error).stack);
                }
                return next();
            }
        });
    }

    /**
     * 获取服务管理器
     */
    getServices(): ServiceManager {
        return this.services;
    }

    /**
     * 获取配置
     */
    getConfig(): Config {
        return this.config;
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig: Partial<Config>, forced: boolean = false): void {
        this.config = { ...this.config, ...newConfig };
        this.ctx.scope.update(this.config, forced);
    }

    /**
     * 清理资源
     */
    async dispose(): Promise<void> {
        await this.middlewareManager.dispose();
        this.services.dispose();
    }
}

/**
 * Agent 工厂函数
 */
export function createAgent(ctx: Context, config: Config): AgentCore {
    return new AgentCore(ctx, config);
}

/**
 * Agent 构建器
 */
export class AgentBuilder {
    private config: Partial<Config> = {};

    constructor(private ctx: Context) {}

    /**
     * 设置配置
     */
    withConfig(config: Partial<Config>): this {
        this.config = { ...this.config, ...config };
        return this;
    }

    /**
     * 启用调试模式
     */
    withDebug(enabled: boolean = true): this {
        this.config.enableDebug = enabled;
        return this;
    }

    /**
     * 设置重试配置
     */
    withRetry(maxRetries: number, timeoutMs: number = 30000): this {
        this.config.maxRetries = maxRetries;
        this.config.retryTimeoutMs = timeoutMs;
        return this;
    }

    /**
     * 构建 Agent
     */
    build(): AgentCore {
        // 使用默认配置填充缺失的配置项
        const defaultConfig = ConfigTransformer.fromOriginalFormat({});
        const finalConfig = { ...defaultConfig, ...this.config };

        return new AgentCore(this.ctx, finalConfig);
    }
}
