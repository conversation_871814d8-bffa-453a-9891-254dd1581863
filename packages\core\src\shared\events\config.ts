import { Schema } from "koishi";

/**
 * 事件系统配置
 */
export interface EventSystemConfig {
    enableEventLogging?: boolean;
    maxEventHistory?: number;
    eventRetentionDays?: number;
    enableHeartbeat?: boolean;
    heartbeatInterval?: number;
}

export const EventSystemConfigSchema: Schema<EventSystemConfig> = Schema.object({
    enableEventLogging: Schema.boolean().default(true).description("是否启用事件日志记录"),
    maxEventHistory: Schema.number().min(100).max(10000).default(1000).description("最大事件历史记录数"),
    eventRetentionDays: Schema.number().min(1).max(365).default(30).description("事件保留天数"),
    enableHeartbeat: Schema.boolean().default(true).description("是否启用心跳事件"),
    heartbeatInterval: Schema.number().min(60).max(3600).default(300).description("心跳间隔（秒）"),
}).description("事件系统配置");
