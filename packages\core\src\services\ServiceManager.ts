import { Context } from "koishi";
import { Config } from "../config";
import { PromptBuilder } from "../prompts/PromptBuilder";
import { ImageProcessor } from "../shared";

/**
 * 简化的服务管理器
 * 直接使用 Koishi Context 和简单的懒加载模式
 */
export class ServiceManager {
    private _promptBuilder?: PromptBuilder;
    private _imageProcessor?: ImageProcessor;
    private _middlewareManager?: MiddlewareManager;

    constructor(
        private ctx: Context,
        private config: Config
    ) {}

    /**
     * 获取提示词构建器
     */
    getPromptBuilder(): PromptBuilder {
        if (!this._promptBuilder) {
            this._promptBuilder = new PromptBuilder(this.ctx, this.config.PromptTemplate);
        }
        return this._promptBuilder;
    }

    /**
     * 获取图片处理器
     */
    getImageProcessor(): ImageProcessor {
        if (!this._imageProcessor) {
            this._imageProcessor = new ImageProcessor(this.ctx);
        }
        return this._imageProcessor;
    }

    /**
     * 获取中间件管理器
     */
    getMiddlewareManager(): MiddlewareManager {
        if (!this._middlewareManager) {
            this._middlewareManager = new MiddlewareManager();
        }
        return this._middlewareManager;
    }

    /**
     * 获取聊天模型切换器
     */
    getChatModelSwitcher() {
        return this.ctx["yesimbot.model"].getChatModelSwitcher(this.config.Chat.UseModel);
    }

    /**
     * 获取数据管理器
     */
    getDataManager() {
        return this.ctx["yesimbot.data"];
    }

    /**
     * 获取工具管理器
     */
    getToolManager() {
        return this.ctx["yesimbot.tool"];
    }

    /**
     * 获取记忆服务
     */
    getMemoryService() {
        return this.ctx["yesimbot.memory"];
    }

    /**
     * 获取模型服务
     */
    getModelService() {
        return this.ctx["yesimbot.model"];
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this._promptBuilder = undefined;
        this._imageProcessor = undefined;
        this._middlewareManager = undefined;
    }
}
